{{-- Auto-Discovery Flyout Navigation untuk FilamentPHP --}}

@php
    use Modules\RajaMenu\Services\FlyoutNavigationService;
    
    // Auto-discover navigation from panel
    $discoveredNav = FlyoutNavigationService::discoverNavigationFromPanel('admin');
    $navigationItems = $discoveredNav['navigationItems'];
    $childItems = $discoveredNav['childItems'];
@endphp

<style>
/* Filament-style navigation CSS */
.auto-flyout-nav {
    display: flex;
    align-items: center;
    gap: 0;
    height: 100%;
}

.nav-group {
    position: relative;
    display: inline-block;
    height: 100%;
}

.nav-group-label {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: rgb(107 114 128);
    cursor: pointer;
    border-radius: 0.375rem;
    transition: all 0.15s ease-in-out;
    height: 100%;
    white-space: nowrap;
}

.nav-group-label:hover {
    background-color: rgb(243 244 246);
    color: rgb(17 24 39);
}

.nav-group-toggle {
    width: 1rem;
    height: 1rem;
    transition: transform 0.15s ease-in-out;
    color: rgb(156 163 175);
}

.nav-group:hover .nav-group-toggle {
    transform: rotate(180deg);
}

.nav-dropdown {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 12rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgb(229 231 235);
    padding: 0.5rem 0;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-0.5rem);
    transition: all 0.15s ease-in-out;
    z-index: 50;
    margin-top: 0.25rem;
}

.nav-group:hover .nav-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 0.5rem 0.75rem;
    color: rgb(75 85 99);
    text-decoration: none;
    transition: all 0.15s ease-in-out;
    position: relative;
    font-size: 0.875rem;
    font-weight: 500;
}

.nav-item:hover {
    background-color: rgb(249 250 251);
    color: rgb(17 24 39);
}

.nav-item.has-children {
    justify-content: space-between;
}

.nav-item.has-children .nav-chevron {
    width: 1rem;
    height: 1rem;
    color: rgb(156 163 175);
    transition: transform 0.15s ease-in-out;
}

.nav-item:hover .nav-chevron {
    transform: translateX(0.125rem);
}

.nav-sub-dropdown {
    position: absolute;
    top: 0;
    left: 100%;
    min-width: 11rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid rgb(229 231 235);
    padding: 0.5rem 0;
    opacity: 0;
    visibility: hidden;
    transform: translateX(-0.5rem);
    transition: all 0.15s ease-in-out;
    z-index: 51;
    margin-left: 0.25rem;
}

.nav-item:hover .nav-sub-dropdown {
    opacity: 1;
    visibility: visible;
    transform: translateX(0);
}

.nav-icon {
    width: 1rem;
    height: 1rem;
    flex-shrink: 0;
    color: rgb(107 114 128);
}

.nav-item:hover .nav-icon {
    color: rgb(59 130 246);
}

.nav-badge {
    background: rgb(239 68 68);
    color: white;
    font-size: 0.625rem;
    font-weight: 600;
    padding: 0.125rem 0.375rem;
    border-radius: 9999px;
    line-height: 1;
    min-width: 1.25rem;
    text-align: center;
}

.nav-type-resource {
    border-left: 2px solid rgb(34 197 94);
    padding-left: 0.625rem;
}

.nav-type-page {
    border-left: 2px solid rgb(59 130 246);
    padding-left: 0.625rem;
}

.nav-type-parent {
    border-left: 2px solid rgb(168 85 247);
    padding-left: 0.625rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .auto-flyout-nav {
        flex-wrap: wrap;
        gap: 0.25rem;
    }

    .nav-group-label {
        padding: 0.375rem 0.5rem;
        font-size: 0.8125rem;
    }

    .nav-dropdown {
        min-width: 10rem;
    }
}
</style>

<nav class="auto-flyout-nav">
    @foreach($navigationItems as $groupName => $items)
        @php
            // Check if any item in this group has children
            $groupHasChildren = false;
            foreach($items as $item) {
                if(isset($childItems[$item['label']]) && count($childItems[$item['label']]) > 0) {
                    $groupHasChildren = true;
                    break;
                }
            }

            // Special handling for Main group - display items directly without group wrapper
            $isMainGroup = $groupName === 'Main';
        @endphp

        @if($isMainGroup)
            {{-- Main group items displayed directly without group wrapper --}}
            @php
                $sortedItems = collect($items)->sortBy('sort')->toArray();
            @endphp

            @foreach($sortedItems as $item)
                <div class="nav-group">
                    <a href="{{ $item['url'] }}" class="nav-group-label" style="text-decoration: none;">
                        @if($item['icon'])
                            <x-filament::icon :icon="$item['icon']" class="nav-icon" style="width: 1rem; height: 1rem; margin-right: 0.5rem;" />
                        @endif
                        <span>{{ $item['label'] }}</span>
                        @if($item['badge'])
                            <span class="nav-badge">{{ $item['badge'] }}</span>
                        @endif
                    </a>
                </div>
            @endforeach
        @else
            {{-- Regular grouped items --}}
            <div class="nav-group">
                <div class="nav-group-label">
                    <span>{{ $groupName }}</span>
                    @if($groupHasChildren)
                        <x-filament::icon icon="heroicon-m-chevron-down" class="nav-group-toggle" />
                    @endif
                </div>

            <div class="nav-dropdown">
                @php
                    // Sort items by sort order
                    $sortedItems = collect($items)->sortBy('sort')->toArray();
                @endphp

                @foreach($sortedItems as $item)
                    @php
                        $hasChildren = isset($childItems[$item['label']]) && count($childItems[$item['label']]) > 0;
                        $isParentType = $item['type'] === 'parent';
                    @endphp

                    @if($hasChildren)
                        {{-- Parent item with children --}}
                        <div class="nav-item has-children nav-type-{{ $item['type'] }}">
                            <div class="flex items-center gap-3">
                                @if($item['icon'])
                                    <x-filament::icon :icon="$item['icon']" class="nav-icon" />
                                @endif
                                <span>{{ $item['label'] }}</span>
                                @if($item['badge'])
                                    <span class="nav-badge">{{ $item['badge'] }}</span>
                                @endif
                            </div>
                            <x-filament::icon icon="heroicon-m-chevron-right" class="nav-chevron" />

                            <div class="nav-sub-dropdown">
                                @php
                                    $sortedChildren = collect($childItems[$item['label']])->sortBy('sort')->toArray();
                                @endphp

                                @foreach($sortedChildren as $childItem)
                                    <a href="{{ $childItem['url'] }}" class="nav-item nav-type-{{ $childItem['type'] }}">
                                        @if($childItem['icon'])
                                            <x-filament::icon :icon="$childItem['icon']" class="nav-icon" />
                                        @endif
                                        <span>{{ $childItem['label'] }}</span>
                                        @if($childItem['badge'])
                                            <span class="nav-badge">{{ $childItem['badge'] }}</span>
                                        @endif
                                    </a>
                                @endforeach
                            </div>
                        </div>
                    @elseif(!$isParentType)
                        {{-- Regular item without children (skip parent type items that don't have children) --}}
                        <a href="{{ $item['url'] }}" class="nav-item nav-type-{{ $item['type'] }}">
                            @if($item['icon'])
                                <x-filament::icon :icon="$item['icon']" class="nav-icon" />
                            @endif
                            <span>{{ $item['label'] }}</span>
                            @if($item['badge'])
                                <span class="nav-badge">{{ $item['badge'] }}</span>
                            @endif
                        </a>
                    @endif
                    {{-- Skip parent type items that don't have children --}}
                @endforeach
            </div>
            </div>
        @endif
    @endforeach
</nav>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Enhanced hover behavior untuk smooth UX
    const navGroups = document.querySelectorAll('.nav-group');
    
    navGroups.forEach(group => {
        const dropdown = group.querySelector('.nav-dropdown');
        let hoverTimeout;
        
        group.addEventListener('mouseenter', function() {
            clearTimeout(hoverTimeout);
        });
        
        group.addEventListener('mouseleave', function() {
            hoverTimeout = setTimeout(() => {
                // Optional: add fade out effect
            }, 100);
        });
    });
    
    // Handle sub-dropdown behavior
    const navItems = document.querySelectorAll('.nav-item.has-children');
    
    navItems.forEach(item => {
        const subDropdown = item.querySelector('.nav-sub-dropdown');
        let subHoverTimeout;
        
        item.addEventListener('mouseenter', function() {
            clearTimeout(subHoverTimeout);
        });
        
        item.addEventListener('mouseleave', function() {
            subHoverTimeout = setTimeout(() => {
                // Optional: add fade out effect
            }, 100);
        });
    });
});
</script>

{{-- Debug info (uncomment for debugging) --}}
@if(false && config('app.debug'))
    <div style="position: fixed; bottom: 10px; right: 10px; background: #1f2937; color: white; padding: 10px; border-radius: 6px; font-size: 12px; z-index: 9999; max-width: 400px; max-height: 300px; overflow-y: auto;">
        <strong>Auto-Discovery Debug:</strong><br>
        Groups: {{ count($navigationItems) }}<br>
        @foreach($navigationItems as $group => $items)
            <strong>{{ $group }}:</strong> {{ count($items) }} items<br>
            @foreach($items as $item)
                &nbsp;&nbsp;- {{ $item['label'] }} ({{ $item['type'] }})
                @if($item['parent_item'])
                    [parent: {{ $item['parent_item'] }}]
                @endif
                <br>
            @endforeach
        @endforeach
        <br><strong>Children:</strong> {{ count($childItems) }} parent items<br>
        @foreach($childItems as $parent => $children)
            <strong>{{ $parent }}:</strong> {{ count($children) }} children<br>
            @foreach($children as $child)
                &nbsp;&nbsp;- {{ $child['label'] }} ({{ $child['type'] }})<br>
            @endforeach
        @endforeach
    </div>
@endif
