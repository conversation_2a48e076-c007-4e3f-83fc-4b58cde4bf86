<?php

return [
    /*
    |--------------------------------------------------------------------------
    | RajaMenu Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for RajaMenu module flyout navigation system
    |
    | NAVIGATION MODES:
    |
    | 1. AUTO MODE ('auto'):
    |    - Automatically discovers navigation from FilamentPHP resources and pages
    |    - Uses navigationGroup, navigationSort, navigationParentItem properties
    |    - Items without group become main menu items
    |    - Items without navigationSort are placed at the end (sort: 9999)
    |
    | 2. MANUAL MODE ('manual'):
    |    - Uses manually defined navigation items from 'manual_navigation' config
    |    - Full control over navigation structure, groups, and hierarchy
    |    - Supports nested children with parent-child relationships
    |    - Custom URLs, icons, badges, and sorting
    |
    */

    'name' => 'RajaMenu',

    /*
    |--------------------------------------------------------------------------
    | Flyout Navigation Settings
    |--------------------------------------------------------------------------
    */
    'flyout' => [
        'enabled' => FALSE,
        'style' => 'simple', // 'simple' or 'complex'
        'position' => 'topbar', // 'topbar' or 'sidebar'
        'animation' => true,
        'hover_delay' => 300, // milliseconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Panel Integration
    |--------------------------------------------------------------------------
    */
    'panels' => [
        'admin' => [
            'enabled' => true,
            'hook' => 'panels::topbar.start',
            'view' => 'rajamenu::navigation.simple-flyout',
        ],
        // Add more panels as needed
    ],

    /*
    |--------------------------------------------------------------------------
    | Navigation Groups
    |--------------------------------------------------------------------------
    */
    'groups' => [
        'cms' => [
            'label' => 'Cms',
            'icon' => 'heroicon-o-book-open',
            'sort' => 2,
        ],
        'system' => [
            'label' => 'System',
            'icon' => 'heroicon-o-cog-6-tooth',
            'sort' => 3,
        ],
        'pengaturan' => [
            'label' => 'Pengaturan',
            'icon' => 'heroicon-o-building-office',
            'sort' => 4,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Assets Configuration
    |--------------------------------------------------------------------------
    */
    'assets' => [
        'css' => [
            'flyout-navigation' => 'modules/rajamenu/css/flyout-navigation.css',
        ],
        'js' => [
            // Add JS assets if needed
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Navigation Mode
    |--------------------------------------------------------------------------
    |
    | Choose between 'auto' (auto-discovery) or 'manual' (custom navigation)
    |
    | 'auto' - Automatically discovers navigation from FilamentPHP resources/pages
    | 'manual' - Uses manually defined navigation items below
    |
    */
    'navigation_mode' => 'auto', // 'auto' or 'manual'

    /*
    |--------------------------------------------------------------------------
    | Auto Discovery
    |--------------------------------------------------------------------------
    */
    'auto_discovery' => [
        'enabled' => true,
        'panels' => ['admin'], // Which panels to auto-discover
        'resources' => true,
        'pages' => true,
        'widgets' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Manual Navigation Items
    |--------------------------------------------------------------------------
    |
    | Define custom navigation items when navigation_mode is set to 'manual'
    | Structure: groups -> items -> children (optional)
    |
    */
    'manual_navigation' => [
        'groups' => [
            'Dashboard' => [
                'label' => 'Dashboard',
                'icon' => 'heroicon-o-home',
                'sort' => -10,
                'items' => [
                    [
                        'label' => 'Dashboard',
                        'icon' => 'heroicon-o-squares-2x2',
                        'url' => '/admin',
                        'sort' => 1,
                        'badge' => null,
                        'type' => 'page',
                    ],
                ],
            ],

            'Content' => [
                'label' => 'Content',
                'icon' => 'heroicon-o-document-text',
                'sort' => -5,
                'items' => [
                    [
                        'label' => 'Content Management',
                        'icon' => 'heroicon-o-book-open',
                        'url' => '#',
                        'sort' => 1,
                        'badge' => null,
                        'type' => 'parent',
                        'children' => [
                            [
                                'label' => 'Articles',
                                'icon' => 'heroicon-o-newspaper',
                                'url' => '/admin/articles',
                                'sort' => 1,
                                'badge' => null,
                                'type' => 'resource',
                            ],
                            [
                                'label' => 'Pages',
                                'icon' => 'heroicon-o-document',
                                'url' => '/admin/pages',
                                'sort' => 2,
                                'badge' => null,
                                'type' => 'resource',
                            ],
                        ],
                    ],
                    [
                        'label' => 'Media Library',
                        'icon' => 'heroicon-o-photo',
                        'url' => '/admin/media',
                        'sort' => 2,
                        'badge' => '45',
                        'type' => 'resource',
                    ],
                ],
            ],

            'System' => [
                'label' => 'System',
                'icon' => 'heroicon-o-cog-6-tooth',
                'sort' => 0,
                'items' => [
                    [
                        'label' => 'Users',
                        'icon' => 'heroicon-o-users',
                        'url' => '/admin/users',
                        'sort' => 1,
                        'badge' => null,
                        'type' => 'resource',
                    ],
                    [
                        'label' => 'Settings',
                        'icon' => 'heroicon-o-wrench-screwdriver',
                        'url' => '#',
                        'sort' => 2,
                        'badge' => null,
                        'type' => 'parent',
                        'children' => [
                            [
                                'label' => 'General Settings',
                                'icon' => 'heroicon-o-cog-8-tooth',
                                'url' => '/admin/settings/general',
                                'sort' => 1,
                                'badge' => null,
                                'type' => 'page',
                            ],
                            [
                                'label' => 'Email Settings',
                                'icon' => 'heroicon-o-envelope',
                                'url' => '/admin/settings/email',
                                'sort' => 2,
                                'badge' => null,
                                'type' => 'page',
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'enabled' => true,
        'ttl' => 3600, // 1 hour
        'key_prefix' => 'rajamenu_',
    ],

    /*
    |--------------------------------------------------------------------------
    | Development Settings
    |--------------------------------------------------------------------------
    */
    'development' => [
        'debug' => env('APP_DEBUG', false),
        'log_navigation_items' => false,
        'show_performance_metrics' => false,
    ],
];
