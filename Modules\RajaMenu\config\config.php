<?php

return [
    /*
    |--------------------------------------------------------------------------
    | RajaMenu Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for RajaMenu module flyout navigation system
    |
    */

    'name' => 'RajaMenu',

    /*
    |--------------------------------------------------------------------------
    | Flyout Navigation Settings
    |--------------------------------------------------------------------------
    */
    'flyout' => [
        'enabled' => FALSE,
        'style' => 'simple', // 'simple' or 'complex'
        'position' => 'topbar', // 'topbar' or 'sidebar'
        'animation' => true,
        'hover_delay' => 300, // milliseconds
    ],

    /*
    |--------------------------------------------------------------------------
    | Panel Integration
    |--------------------------------------------------------------------------
    */
    'panels' => [
        'admin' => [
            'enabled' => true,
            'hook' => 'panels::topbar.start',
            'view' => 'rajamenu::navigation.simple-flyout',
        ],
        // Add more panels as needed
    ],

    /*
    |--------------------------------------------------------------------------
    | Navigation Groups
    |--------------------------------------------------------------------------
    */
    'groups' => [
        'cms' => [
            'label' => 'Cms',
            'icon' => 'heroicon-o-book-open',
            'sort' => 2,
        ],
        'system' => [
            'label' => 'System',
            'icon' => 'heroicon-o-cog-6-tooth',
            'sort' => 3,
        ],
        'pengaturan' => [
            'label' => 'Pengaturan',
            'icon' => 'heroicon-o-building-office',
            'sort' => 4,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Assets Configuration
    |--------------------------------------------------------------------------
    */
    'assets' => [
        'css' => [
            'flyout-navigation' => 'modules/rajamenu/css/flyout-navigation.css',
        ],
        'js' => [
            // Add JS assets if needed
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Auto Discovery
    |--------------------------------------------------------------------------
    */
    'auto_discovery' => [
        'enabled' => true,
        'panels' => ['admin'], // Which panels to auto-discover
        'resources' => true,
        'pages' => true,
        'widgets' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Cache Settings
    |--------------------------------------------------------------------------
    */
    'cache' => [
        'enabled' => true,
        'ttl' => 3600, // 1 hour
        'key_prefix' => 'rajamenu_',
    ],

    /*
    |--------------------------------------------------------------------------
    | Development Settings
    |--------------------------------------------------------------------------
    */
    'development' => [
        'debug' => env('APP_DEBUG', false),
        'log_navigation_items' => false,
        'show_performance_metrics' => false,
    ],
];
