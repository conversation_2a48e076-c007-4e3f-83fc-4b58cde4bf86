<?php

namespace Modules\RajaMenu\Services;

use Filament\Navigation\NavigationItem;
use Illuminate\Support\Collection;

class FlyoutNavigationService
{
    /**
     * Organize navigation items into parent-child structure for flyout menu
     */
    public static function organizeNavigationItems(array $navigationItems): array
    {
        $organized = [
            'groups' => [],
            'childItems' => []
        ];

        $groupedItems = collect($navigationItems)->groupBy(function ($item) {
            return $item->getGroup() ?? 'Default';
        });

        foreach ($groupedItems as $groupName => $items) {
            $parentItems = [];
            $childItems = [];

            foreach ($items as $item) {
                $parentItem = $item->getParentItem();
                
                if ($parentItem) {
                    // This is a child item
                    if (!isset($childItems[$parentItem])) {
                        $childItems[$parentItem] = [];
                    }
                    $childItems[$parentItem][] = $item;
                } else {
                    // This is a parent item
                    $parentItems[] = $item;
                }
            }

            $organized['groups'][$groupName] = $parentItems;
            $organized['childItems'] = array_merge($organized['childItems'], $childItems);
        }

        return $organized;
    }

    /**
     * Create navigation items for CMS group with flyout structure
     */
    public static function createCmsNavigationItems(): array
    {
        return [
            // Parent: Content Management
            // NavigationItem::make('Content')
            //     ->icon('heroicon-o-book-open')
            //     ->url('#')
            //     ->group('Cms')
            //     ->sort(1),

            // // Child items untuk Content
            // NavigationItem::make('Konten')
            //     ->icon('heroicon-o-document-text')
            //     ->url(fn(): string => \App\Filament\Resources\CmsResource::getUrl('index'))
            //     ->group('Cms')
            //     ->parentItem('Content')
            //     ->sort(1),

            // NavigationItem::make('Artikel')
            //     ->icon('heroicon-o-newspaper')
            //     ->url(fn(): string => \App\Filament\Pages\Cms\Kategori::getUrl())
            //     ->group('Cms')
            //     ->parentItem('Content')
            //     ->sort(2),

            // NavigationItem::make('Media Library')
            //     ->icon('heroicon-o-photo')
            //     ->url('#')
            //     ->group('Cms')
            //     ->parentItem('Content')
            //     ->sort(3),

            // NavigationItem::make('Kategori artikel')
            //     ->icon('heroicon-o-tag')
            //     ->url(fn(): string => \App\Filament\Pages\Cms\Kategori::getUrl())
            //     ->group('Cms')
            //     ->parentItem('Content')
            //     ->sort(4),

            // NavigationItem::make('Menu')
            //     ->icon('heroicon-o-queue-list')
            //     ->url(fn(): string => \App\Filament\Resources\MenuWebsiteResource::getUrl('index'))
            //     ->group('Cms')
            //     ->parentItem('Content')
            //     ->sort(5),

            // NavigationItem::make('Tema')
            //     ->icon('heroicon-o-paint-brush')
            //     ->url(fn(): string => \App\Filament\Resources\CmsResource::getUrl('tema'))
            //     ->group('Cms')
            //     ->parentItem('Content')
            //     ->sort(6),

            // // Parent: Design
            // NavigationItem::make('Design')
            //     ->icon('heroicon-o-paint-brush')
            //     ->url('#')
            //     ->group('Cms')
            //     ->sort(10),

            // NavigationItem::make('Templates')
            //     ->icon('heroicon-o-document-duplicate')
            //     ->url('#')
            //     ->group('Cms')
            //     ->parentItem('Design')
            //     ->sort(1),

            // NavigationItem::make('Widgets')
            //     ->icon('heroicon-o-squares-2x2')
            //     ->url('#')
            //     ->group('Cms')
            //     ->parentItem('Design')
            //     ->sort(2),
        ];
    }

    /**
     * Create navigation items for System group with flyout structure
     */
    public static function createSystemNavigationItems(): array
    {
        return [
            // // Parent: Data Management
            // NavigationItem::make('Data Management')
            //     ->icon('heroicon-o-database')
            //     ->url('#')
            //     ->group('System')
            //     ->sort(1),

            // NavigationItem::make('Seluruh kategori')
            //     ->icon('heroicon-o-list-bullet')
            //     ->url(fn(): string => \App\Filament\Resources\KategoriResource::getUrl('index'))
            //     ->group('System')
            //     ->parentItem('Data Management')
            //     ->sort(1),

            // NavigationItem::make('Metode pembayaran')
            //     ->icon('heroicon-o-credit-card')
            //     ->url(fn(): string => \App\Filament\Resources\MetodePembayaranUtamaResource::getUrl('index'))
            //     ->group('System')
            //     ->parentItem('Data Management')
            //     ->sort(2),

            // NavigationItem::make('Data')
            //     ->icon('heroicon-o-table-cells')
            //     ->url(fn(): string => \App\Filament\Pages\Data::getUrl())
            //     ->group('System')
            //     ->parentItem('Data Management')
            //     ->sort(3),

            // // Parent: System Tools
            // NavigationItem::make('System Tools')
            //     ->icon('heroicon-o-wrench-screwdriver')
            //     ->url('#')
            //     ->group('System')
            //     ->sort(10),

            // NavigationItem::make('Rute')
            //     ->icon('heroicon-o-map')
            //     ->url(fn(): string => \App\Filament\Pages\RouteList::getUrl())
            //     ->group('System')
            //     ->parentItem('System Tools')
            //     ->sort(1),

            // NavigationItem::make('Aplikasi')
            //     ->icon('heroicon-o-squares-plus')
            //     ->url(fn(): string => \App\Filament\Pages\AplikasiRajaDesain::getUrl())
            //     ->group('System')
            //     ->parentItem('System Tools')
            //     ->sort(2),

            // NavigationItem::make('Konfig')
            //     ->icon('heroicon-o-cog-6-tooth')
            //     ->url(fn(): string => \App\Filament\Resources\KonfigResource::getUrl('index'))
            //     ->group('System')
            //     ->parentItem('System Tools')
            //     ->sort(3),
        ];
    }

    /**
     * Create navigation items for Settings group with flyout structure
     */
    public static function createSettingsNavigationItems(): array
    {
        return [
            // Parent: Business Management
            // NavigationItem::make('Business')
            //     ->icon('heroicon-o-building-office')
            //     ->url('#')
            //     ->group('Pengaturan')
            //     ->sort(1),

            // NavigationItem::make('Usaha')
            //     ->icon('heroicon-o-building-storefront')
            //     ->url(fn(): string => \App\Filament\Resources\TokoResource::getUrl('index'))
            //     ->group('Pengaturan')
            //     ->parentItem('Business')
            //     ->sort(1),

            // NavigationItem::make('Staff')
            //     ->icon('heroicon-o-users')
            //     ->url(fn(): string => \App\Filament\Resources\UserResource::getUrl('index'))
            //     ->group('Pengaturan')
            //     ->parentItem('Business')
            //     ->sort(2),
        ];
    }

    /**
     * Get all organized navigation items
     */
    // public static function getAllNavigationItems(): array
    // {
    //     return array_merge(
    //         self::createCmsNavigationItems(),
    //         self::createSystemNavigationItems(),
    //         self::createSettingsNavigationItems()
    //     );
    // }

    /**
     * Auto-discover navigation from panel resources
     */
    public static function discoverNavigationFromPanel(string $panelId = 'admin'): array
    {
        try {
            $panel = \Filament\Facades\Filament::getPanel($panelId);
            $navigationItems = [];
            $childItems = [];

            // Get all resources from panel
            $resources = $panel->getResources();
            $pages = $panel->getPages();

            // Process resources
            foreach ($resources as $resource) {
                $item = self::extractNavigationFromResource($resource);
                if ($item) {
                    if ($item['parent_item']) {
                        $childItems[$item['parent_item']][] = $item;
                    } else {
                        $navigationItems[$item['group']][] = $item;
                    }
                }
            }

            // Process pages
            foreach ($pages as $page) {
                $item = self::extractNavigationFromPage($page);
                if ($item) {
                    if ($item['parent_item']) {
                        $childItems[$item['parent_item']][] = $item;
                    } else {
                        $navigationItems[$item['group']][] = $item;
                    }
                }
            }

            // Only add parent items that don't exist as actual resources/pages
            // Don't create parent items that already exist as standalone items
            self::addMissingParentItems($navigationItems, $childItems);

            return [
                'navigationItems' => $navigationItems,
                'childItems' => $childItems,
            ];
        } catch (\Exception $e) {
            // Fallback to static items if panel not available
            return [
                'navigationItems' => self::getNavigationItems(),
                'childItems' => [],
            ];
        }
    }

    /**
     * Remove standalone parent items that have children (they will be recreated as parent-only items)
     */
    protected static function removeStandaloneParentItems(array &$navigationItems, array $childItems): void
    {
        $parentLabels = array_keys($childItems);

        foreach ($navigationItems as $groupName => &$items) {
            $originalCount = count($items);
            $items = array_filter($items, function($item) use ($parentLabels) {
                // Keep item if it's not a parent that has children
                $shouldRemove = in_array($item['label'], $parentLabels);
                return !$shouldRemove;
            });
            // Re-index array to avoid gaps
            $items = array_values($items);

            // Debug log
            if (config('app.debug') && $originalCount !== count($items)) {
                \Log::info("Removed parent items from group {$groupName}: {$originalCount} -> " . count($items));
            }
        }
    }

    /**
     * Add missing parent items that are referenced but don't exist
     */
    protected static function addMissingParentItems(array &$navigationItems, array $childItems): void
    {
        $parentItemsNeeded = array_keys($childItems);

        foreach ($parentItemsNeeded as $parentLabel) {
            // Skip empty parent labels
            if (empty($parentLabel)) {
                continue;
            }

            // Check if this parent has any valid children
            if (!isset($childItems[$parentLabel]) || empty($childItems[$parentLabel])) {
                continue;
            }

            $found = false;

            // Check if parent exists in any group
            foreach ($navigationItems as $group => $items) {
                foreach ($items as $item) {
                    if ($item['label'] === $parentLabel) {
                        $found = true;
                        break 2;
                    }
                }
            }

            // If parent doesn't exist, create it only if it has valid children
            if (!$found && !empty($childItems[$parentLabel])) {
                $parentGroup = self::guessParentGroup($parentLabel, $childItems);
                $parentIcon = self::guessParentIcon($parentLabel);

                $navigationItems[$parentGroup][] = [
                    'label' => $parentLabel,
                    'icon' => $parentIcon,
                    'url' => '#',
                    'group' => $parentGroup,
                    'parent_item' => null,
                    'sort' => 0,
                    'badge' => null,
                    'type' => 'parent',
                    'class' => null,
                ];
            }
        }
    }

    /**
     * Guess the group for a parent item based on its children
     */
    protected static function guessParentGroup(string $parentLabel, array $childItems): string
    {
        if (!isset($childItems[$parentLabel]) || empty($childItems[$parentLabel])) {
            return 'Default';
        }

        // Get the most common group from children
        $groups = array_column($childItems[$parentLabel], 'group');
        $groupCounts = array_count_values($groups);
        arsort($groupCounts);

        return array_key_first($groupCounts) ?: 'Default';
    }

    /**
     * Guess the icon for a parent item based on its label
     */
    protected static function guessParentIcon(string $parentLabel): string
    {
        $iconMap = [
            'Business' => 'heroicon-o-building-office',
            'Data Management' => 'heroicon-o-circle-stack',
            'System Tools' => 'heroicon-o-wrench-screwdriver',
            'Content' => 'heroicon-o-document-text',
            'Settings' => 'heroicon-o-cog-6-tooth',
            'Users' => 'heroicon-o-users',
            'Reports' => 'heroicon-o-chart-bar',
            'Analytics' => 'heroicon-o-chart-pie',
        ];

        return $iconMap[$parentLabel] ?? 'heroicon-o-document';
    }

    /**
     * Extract navigation info from resource
     */
    protected static function extractNavigationFromResource(string $resource): ?array
    {
        try {
            // Check if resource should register navigation
            if (method_exists($resource, 'shouldRegisterNavigation') && !$resource::shouldRegisterNavigation()) {
                return null;
            }

            // Get navigation properties
            $label = method_exists($resource, 'getNavigationLabel') ? $resource::getNavigationLabel() : null;
            $icon = method_exists($resource, 'getNavigationIcon') ? $resource::getNavigationIcon() : 'heroicon-o-document';
            $group = method_exists($resource, 'getNavigationGroup') ? $resource::getNavigationGroup() : null;
            $parentItem = method_exists($resource, 'getNavigationParentItem') ? $resource::getNavigationParentItem() : null;
            $sort = method_exists($resource, 'getNavigationSort') ? $resource::getNavigationSort() : null;

            // Jika tidak memiliki navigationSort, tempatkan di paling akhir
            if ($sort === null) {
                $sort = 9999;
            }
            $badge = method_exists($resource, 'getNavigationBadge') ? $resource::getNavigationBadge() : null;

            if (!$label) {
                return null;
            }

            // Jika tidak memiliki group dan tidak memiliki parent, jadikan sebagai parent menu
            if (!$group && !$parentItem) {
                $group = $label; // Gunakan label sebagai group name
            } elseif (!$group && $parentItem) {
                $group = 'Default'; // Will be moved to correct group by parent logic
            }

            return [
                'label' => $label,
                'icon' => $icon,
                'url' => $resource::getUrl('index'),
                'group' => $group,
                'parent_item' => $parentItem,
                'sort' => $sort ?? 0,
                'badge' => $badge,
                'type' => 'resource',
                'class' => $resource,
            ];
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Extract navigation info from page
     */
    protected static function extractNavigationFromPage(string $page): ?array
    {
        try {
            // Check if page should register navigation
            if (method_exists($page, 'shouldRegisterNavigation') && !$page::shouldRegisterNavigation()) {
                return null;
            }

            // Get navigation properties
            $label = method_exists($page, 'getNavigationLabel') ? $page::getNavigationLabel() : null;
            $icon = method_exists($page, 'getNavigationIcon') ? $page::getNavigationIcon() : 'heroicon-o-document';
            $group = method_exists($page, 'getNavigationGroup') ? $page::getNavigationGroup() : null;
            $parentItem = method_exists($page, 'getNavigationParentItem') ? $page::getNavigationParentItem() : null;
            $sort = method_exists($page, 'getNavigationSort') ? $page::getNavigationSort() : null;

            // Jika tidak memiliki navigationSort, tempatkan di paling akhir
            if ($sort === null) {
                $sort = 9999;
            }

            if (!$label) {
                return null;
            }

            // Jika tidak memiliki group dan tidak memiliki parent, jadikan sebagai menu utama tanpa group
            if (!$group && !$parentItem) {
                $group = 'Main'; // Gunakan group khusus untuk menu utama tanpa group
            } elseif (!$group && $parentItem) {
                $group = 'Default'; // Will be moved to correct group by parent logic
            }

            return [
                'label' => $label,
                'icon' => $icon,
                'url' => $page::getUrl(),
                'group' => $group,
                'parent_item' => $parentItem,
                'sort' => $sort ?? 0,
                'badge' => null,
                'type' => 'page',
                'class' => $page,
            ];
        } catch (\Exception $e) {
            return null;
        }
    }

    /**
     * Get navigation items organized by groups (simple array format)
     */
    // public static function getNavigationItems(): array
    // {
    //     return [
    //         'Cms' => [
    //             [
    //                 'label' => 'Content',
    //                 'icon' => 'heroicon-o-book-open',
    //                 'url' => '#',
    //             ]
    //         ],
    //         'System' => [
    //             [
    //                 'label' => 'Data Management',
    //                 'icon' => 'heroicon-o-database',
    //                 'url' => '#',
    //             ],
    //             [
    //                 'label' => 'System Tools',
    //                 'icon' => 'heroicon-o-wrench-screwdriver',
    //                 'url' => '#',
    //             ]
    //         ],
    //         'Pengaturan' => [
    //             [
    //                 'label' => 'Business',
    //                 'icon' => 'heroicon-o-building-office',
    //                 'url' => '#',
    //             ]
    //         ]
    //     ];
    // }


}
