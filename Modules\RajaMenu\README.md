# RajaMenu Module

Modul untuk mengelola flyout navigation di panel FilamentPHP dengan child menu yang tampil di samping saat mouseover.

## 📁 Struktur Modul

```
Modules/RajaMenu/
├── app/
│   ├── Providers/
│   │   ├── RajaMenuServiceProvider.php
│   │   └── RajaMenuPanelProvider.php
│   ├── Services/
│   │   └── FlyoutNavigationService.php
│   └── Traits/
│       └── HasFlyoutNavigation.php
├── config/
│   └── config.php
├── resources/
│   ├── assets/
│   │   └── css/
│   │       └── flyout-navigation.css
│   └── views/
│       └── navigation/
│           ├── simple-flyout.blade.php
│           └── flyout-navigation.blade.php
└── README.md
```

## 🚀 Instalasi

### 1. Aktifkan Modul
```bash
php artisan module:enable RajaMenu
```

### 2. Publish Assets
```bash
php artisan vendor:publish --tag=rajamenu-flyout-assets
```

### 3. Gunakan Trait di Panel Provider
```php
use Modules\RajaMenu\Traits\HasFlyoutNavigation;

class AdminPanelProvider extends PanelProvider
{
    use HasFlyoutNavigation;

    public function panel(Panel $panel): Panel
    {
        $panel = $panel
            ->default()
            ->id('admin')
            ->topNavigation(); // Pastikan top navigation aktif

        // Apply flyout navigation
        $panel = $this->applyFlyoutNavigation($panel);

        return $panel;
    }
}
```

## 🔧 Mode Navigation

### Mode Auto (Auto-Discovery) - Default

Mode yang otomatis mengambil navigasi dari FilamentPHP resources dan pages.

**Konfigurasi:**
```php
// config/config.php
'navigation_mode' => 'auto',
```

**Pengaturan di Resource/Page:**
```php
class MyResource extends Resource
{
    protected static ?string $navigationGroup = 'Content';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationParentItem = 'Content Management';
    protected static ?string $navigationLabel = 'Articles';
    protected static ?string $navigationIcon = 'heroicon-o-newspaper';

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
```

**Aturan Auto-Discovery:**
- Item tanpa group → Menu utama langsung
- Item dengan group → Dropdown group
- Item dengan parent → Sub-menu flyout
- Item tanpa navigationSort → Ditempatkan di akhir (sort: 9999)

### Mode Manual

Kontrol penuh dengan konfigurasi manual.

**Konfigurasi:**
```php
// config/config.php
'navigation_mode' => 'manual',

'manual_navigation' => [
    'groups' => [
        'Dashboard' => [
            'label' => 'Dashboard',
            'icon' => 'heroicon-o-home',
            'sort' => -10,
            'items' => [
                [
                    'label' => 'Dashboard',
                    'icon' => 'heroicon-o-squares-2x2',
                    'url' => '/admin',
                    'sort' => 1,
                    'badge' => null,
                    'type' => 'page',
                ],
            ],
        ],
        'Content' => [
            'label' => 'Content',
            'icon' => 'heroicon-o-document-text',
            'sort' => -5,
            'items' => [
                [
                    'label' => 'Content Management',
                    'icon' => 'heroicon-o-book-open',
                    'url' => '#',
                    'sort' => 1,
                    'type' => 'parent',
                    'children' => [
                        [
                            'label' => 'Articles',
                            'icon' => 'heroicon-o-newspaper',
                            'url' => '/admin/articles',
                            'sort' => 1,
                            'type' => 'resource',
                        ],
                    ],
                ],
            ],
        ],
    ],
],
```

## Support

you can join our discord server to get support [TomatoPHP](https://discord.gg/Xqmt35Uh)

## Docs

you can check docs of this package on [Docs](https://docs.tomatophp.com/plugins/tomato-themes)

## Changelog

Please see [CHANGELOG](CHANGELOG.md) for more information on what has changed recently.

## Security

Please see [SECURITY](SECURITY.md) for more information about security.

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.
