# RajaMenu Module

Modul untuk mengelola flyout navigation di panel FilamentPHP dengan child menu yang tampil di samping saat mouseover.

## 📁 Struktur Modul

```
Modules/RajaMenu/
├── app/
│   ├── Providers/
│   │   ├── RajaMenuServiceProvider.php
│   │   └── RajaMenuPanelProvider.php
│   ├── Services/
│   │   └── FlyoutNavigationService.php
│   └── Traits/
│       └── HasFlyoutNavigation.php
├── config/
│   └── config.php
├── resources/
│   ├── assets/
│   │   └── css/
│   │       └── flyout-navigation.css
│   └── views/
│       └── navigation/
│           ├── simple-flyout.blade.php
│           └── flyout-navigation.blade.php
└── README.md
```

## 🚀 Instalasi

### 1. Aktifkan Modul
```bash
php artisan module:enable RajaMenu
```

### 2. Publish Assets
```bash
php artisan vendor:publish --tag=rajamenu-flyout-assets
```

### 3. Gunakan Trait di Panel Provider
```php
use Modules\RajaMenu\Traits\HasFlyoutNavigation;

class AdminPanelProvider extends PanelProvider
{
    use HasFlyoutNavigation;

    public function panel(Panel $panel): Panel
    {
        $panel = $panel
            ->default()
            ->id('admin')
            ->topNavigation(); // Pastikan top navigation aktif

        // Apply flyout navigation
        $panel = $this->applyFlyoutNavigation($panel);

        return $panel;
    }
}
```

## 🔧 Mode Navigation

### Mode Auto (Auto-Discovery) - Default

Mode yang otomatis mengambil navigasi dari FilamentPHP resources dan pages.

**Konfigurasi:**
```php
// config/config.php
'navigation_mode' => 'auto',
```

**Pengaturan di Resource/Page:**
```php
class MyResource extends Resource
{
    protected static ?string $navigationGroup = 'Content';
    protected static ?int $navigationSort = 1;
    protected static ?string $navigationParentItem = 'Content Management';
    protected static ?string $navigationLabel = 'Articles';
    protected static ?string $navigationIcon = 'heroicon-o-newspaper';

    public static function getNavigationBadge(): ?string
    {
        return static::getModel()::count();
    }
}
```

**Aturan Auto-Discovery:**
- Item tanpa group → Menu utama langsung
- Item dengan group → Dropdown group
- Item dengan parent → Sub-menu flyout
- Item tanpa navigationSort → Ditempatkan di akhir (sort: 9999)

### Mode Manual

Kontrol penuh dengan konfigurasi manual.

**Contoh Konfigurasi Lengkap:**
```php
// config/config.php
'navigation_mode' => 'manual',

'manual_navigation' => [
    'groups' => [
        // Menu utama tanpa dropdown
        'Dashboard' => [
            'label' => 'Dashboard',
            'icon' => 'heroicon-o-home',
            'sort' => -10,
            'items' => [
                [
                    'label' => 'Dashboard',
                    'icon' => 'heroicon-o-squares-2x2',
                    'url' => '/admin',
                    'sort' => 1,
                    'badge' => null,
                    'type' => 'page',
                ],
            ],
        ],

        // Group dengan dropdown dan sub-menu
        'Content' => [
            'label' => 'Content',
            'icon' => 'heroicon-o-document-text',
            'sort' => -5,
            'items' => [
                // Parent item dengan children (flyout sub-menu)
                [
                    'label' => 'Content Management',
                    'icon' => 'heroicon-o-book-open',
                    'url' => '#',
                    'sort' => 1,
                    'badge' => null,
                    'type' => 'parent',
                    'children' => [
                        [
                            'label' => 'Articles',
                            'icon' => 'heroicon-o-newspaper',
                            'url' => '/admin/articles',
                            'sort' => 1,
                            'badge' => '25',
                            'type' => 'resource',
                        ],
                        [
                            'label' => 'Pages',
                            'icon' => 'heroicon-o-document',
                            'url' => '/admin/pages',
                            'sort' => 2,
                            'badge' => null,
                            'type' => 'resource',
                        ],
                        [
                            'label' => 'Categories',
                            'icon' => 'heroicon-o-tag',
                            'url' => '/admin/categories',
                            'sort' => 3,
                            'badge' => '8',
                            'type' => 'resource',
                        ],
                    ],
                ],
                // Item langsung tanpa children
                [
                    'label' => 'Media Library',
                    'icon' => 'heroicon-o-photo',
                    'url' => '/admin/media',
                    'sort' => 2,
                    'badge' => '156',
                    'type' => 'resource',
                ],
                [
                    'label' => 'File Manager',
                    'icon' => 'heroicon-o-folder',
                    'url' => '/admin/files',
                    'sort' => 3,
                    'badge' => null,
                    'type' => 'page',
                ],
            ],
        ],

        // Group E-commerce
        'Ecommerce' => [
            'label' => 'E-commerce',
            'icon' => 'heroicon-o-shopping-cart',
            'sort' => -3,
            'items' => [
                [
                    'label' => 'Products',
                    'icon' => 'heroicon-o-cube',
                    'url' => '/admin/products',
                    'sort' => 1,
                    'badge' => '342',
                    'type' => 'resource',
                ],
                [
                    'label' => 'Orders',
                    'icon' => 'heroicon-o-shopping-bag',
                    'url' => '/admin/orders',
                    'sort' => 2,
                    'badge' => '12',
                    'type' => 'resource',
                ],
                [
                    'label' => 'Inventory',
                    'icon' => 'heroicon-o-archive-box',
                    'url' => '#',
                    'sort' => 3,
                    'badge' => null,
                    'type' => 'parent',
                    'children' => [
                        [
                            'label' => 'Stock Management',
                            'icon' => 'heroicon-o-squares-plus',
                            'url' => '/admin/inventory/stock',
                            'sort' => 1,
                            'badge' => null,
                            'type' => 'page',
                        ],
                        [
                            'label' => 'Warehouses',
                            'icon' => 'heroicon-o-building-office-2',
                            'url' => '/admin/inventory/warehouses',
                            'sort' => 2,
                            'badge' => '3',
                            'type' => 'resource',
                        ],
                    ],
                ],
            ],
        ],

        // Group Users & Permissions
        'Users' => [
            'label' => 'Users & Roles',
            'icon' => 'heroicon-o-users',
            'sort' => 0,
            'items' => [
                [
                    'label' => 'Users',
                    'icon' => 'heroicon-o-user-group',
                    'url' => '/admin/users',
                    'sort' => 1,
                    'badge' => '89',
                    'type' => 'resource',
                ],
                [
                    'label' => 'Roles & Permissions',
                    'icon' => 'heroicon-o-shield-check',
                    'url' => '#',
                    'sort' => 2,
                    'badge' => null,
                    'type' => 'parent',
                    'children' => [
                        [
                            'label' => 'Roles',
                            'icon' => 'heroicon-o-identification',
                            'url' => '/admin/roles',
                            'sort' => 1,
                            'badge' => '5',
                            'type' => 'resource',
                        ],
                        [
                            'label' => 'Permissions',
                            'icon' => 'heroicon-o-key',
                            'url' => '/admin/permissions',
                            'sort' => 2,
                            'badge' => null,
                            'type' => 'resource',
                        ],
                    ],
                ],
            ],
        ],

        // Group System & Settings
        'System' => [
            'label' => 'System',
            'icon' => 'heroicon-o-cog-6-tooth',
            'sort' => 10,
            'items' => [
                [
                    'label' => 'Settings',
                    'icon' => 'heroicon-o-wrench-screwdriver',
                    'url' => '#',
                    'sort' => 1,
                    'badge' => null,
                    'type' => 'parent',
                    'children' => [
                        [
                            'label' => 'General Settings',
                            'icon' => 'heroicon-o-cog-8-tooth',
                            'url' => '/admin/settings/general',
                            'sort' => 1,
                            'badge' => null,
                            'type' => 'page',
                        ],
                        [
                            'label' => 'Email Settings',
                            'icon' => 'heroicon-o-envelope',
                            'url' => '/admin/settings/email',
                            'sort' => 2,
                            'badge' => null,
                            'type' => 'page',
                        ],
                        [
                            'label' => 'Payment Settings',
                            'icon' => 'heroicon-o-credit-card',
                            'url' => '/admin/settings/payment',
                            'sort' => 3,
                            'badge' => null,
                            'type' => 'page',
                        ],
                    ],
                ],
                [
                    'label' => 'Logs',
                    'icon' => 'heroicon-o-document-text',
                    'url' => '/admin/logs',
                    'sort' => 2,
                    'badge' => null,
                    'type' => 'page',
                ],
                [
                    'label' => 'Backups',
                    'icon' => 'heroicon-o-archive-box-arrow-down',
                    'url' => '/admin/backups',
                    'sort' => 3,
                    'badge' => null,
                    'type' => 'page',
                ],
            ],
        ],

        // Group Reports & Analytics
        'Reports' => [
            'label' => 'Reports',
            'icon' => 'heroicon-o-chart-bar',
            'sort' => 15,
            'items' => [
                [
                    'label' => 'Sales Reports',
                    'icon' => 'heroicon-o-chart-pie',
                    'url' => '/admin/reports/sales',
                    'sort' => 1,
                    'badge' => null,
                    'type' => 'page',
                ],
                [
                    'label' => 'Analytics',
                    'icon' => 'heroicon-o-presentation-chart-line',
                    'url' => '#',
                    'sort' => 2,
                    'badge' => null,
                    'type' => 'parent',
                    'children' => [
                        [
                            'label' => 'Traffic Analytics',
                            'icon' => 'heroicon-o-globe-alt',
                            'url' => '/admin/analytics/traffic',
                            'sort' => 1,
                            'badge' => null,
                            'type' => 'page',
                        ],
                        [
                            'label' => 'User Behavior',
                            'icon' => 'heroicon-o-cursor-arrow-rays',
                            'url' => '/admin/analytics/behavior',
                            'sort' => 2,
                            'badge' => null,
                            'type' => 'page',
                        ],
                    ],
                ],
            ],
        ],
    ],
],
```

### 📋 Struktur Item Manual

#### **Group Structure:**
```php
'GroupName' => [
    'label' => 'Display Name',        // Nama yang ditampilkan
    'icon' => 'heroicon-o-icon',     // Icon group (opsional)
    'sort' => -10,                   // Urutan group
    'items' => [                     // Array items dalam group
        // ... items
    ],
],
```

#### **Item Structure:**
```php
[
    'label' => 'Item Name',           // Wajib: Nama menu item
    'icon' => 'heroicon-o-icon',     // Opsional: Icon Heroicons
    'url' => '/admin/path',          // Wajib: URL tujuan (gunakan '#' untuk parent)
    'sort' => 1,                     // Opsional: Urutan dalam group (default: 9999)
    'badge' => '10',                 // Opsional: Badge counter (string/null)
    'type' => 'resource',            // Opsional: Tipe (resource/page/parent)
    'children' => [                  // Opsional: Sub-menu items (hanya untuk parent)
        [
            'label' => 'Child Item',
            'icon' => 'heroicon-o-icon',
            'url' => '/admin/child-path',
            'sort' => 1,
            'badge' => null,
            'type' => 'resource',
        ],
        // ... more children
    ],
]
```

### 🎯 Tips Penggunaan Mode Manual

#### **1. Sorting & Urutan:**
```php
// Urutan group berdasarkan sort value:
'Dashboard' => ['sort' => -10],  // Posisi 1
'Content' => ['sort' => -5],     // Posisi 2
'Ecommerce' => ['sort' => -3],   // Posisi 3
'Users' => ['sort' => 0],        // Posisi 4
'System' => ['sort' => 10],      // Posisi 5
'Reports' => ['sort' => 15],     // Posisi 6
```

#### **2. Icons yang Direkomendasikan:**
```php
// Dashboard & Home
'heroicon-o-home'
'heroicon-o-squares-2x2'

// Content & Media
'heroicon-o-document-text'
'heroicon-o-newspaper'
'heroicon-o-photo'
'heroicon-o-folder'

// E-commerce
'heroicon-o-shopping-cart'
'heroicon-o-cube'
'heroicon-o-shopping-bag'
'heroicon-o-credit-card'

// Users & Security
'heroicon-o-users'
'heroicon-o-user-group'
'heroicon-o-shield-check'
'heroicon-o-key'

// System & Settings
'heroicon-o-cog-6-tooth'
'heroicon-o-wrench-screwdriver'
'heroicon-o-chart-bar'
```

#### **3. Badge Usage:**
```php
// Static badge
'badge' => '25'

// Dynamic badge (gunakan di auto mode)
public static function getNavigationBadge(): ?string
{
    return static::getModel()::count();
}

// No badge
'badge' => null
```

#### **4. URL Patterns:**
```php
// FilamentPHP resource URLs
'url' => '/admin/users'           // UserResource
'url' => '/admin/posts'           // PostResource

// FilamentPHP page URLs
'url' => '/admin/settings'        // SettingsPage
'url' => '/admin/dashboard'       // Dashboard

// External URLs
'url' => 'https://example.com'    // External link

// Parent items (no direct URL)
'url' => '#'                      // Parent dengan children
```

#### **5. Nested Navigation (3 Level):**
```php
'items' => [
    [
        'label' => 'Level 1 Parent',
        'url' => '#',
        'type' => 'parent',
        'children' => [
            [
                'label' => 'Level 2 Parent',
                'url' => '#',
                'type' => 'parent',
                'children' => [
                    [
                        'label' => 'Level 3 Item',
                        'url' => '/admin/deep-item',
                        'type' => 'page',
                    ],
                ],
            ],
        ],
    ],
]
```

### ⚡ Quick Start Manual Mode

1. **Set mode manual:**
```php
'navigation_mode' => 'manual',
```

2. **Copy contoh konfigurasi** dari atas ke `manual_navigation.groups`

3. **Sesuaikan URLs** dengan routes FilamentPHP Anda

4. **Test navigation** di browser

5. **Customize icons, badges, dan sorting** sesuai kebutuhan

## Support

you can join our discord server to get support [TomatoPHP](https://discord.gg/Xqmt35Uh)

## Docs

you can check docs of this package on [Docs](https://docs.tomatophp.com/plugins/tomato-themes)

## Changelog

Please see [CHANGELOG](CHANGELOG.md) for more information on what has changed recently.

## Security

Please see [SECURITY](SECURITY.md) for more information about security.

## License

The MIT License (MIT). Please see [License File](LICENSE.md) for more information.
