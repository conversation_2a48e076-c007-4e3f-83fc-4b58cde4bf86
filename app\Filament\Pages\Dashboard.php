<?php 
namespace App\Filament\Pages;

use App\Filament\Widgets\IconPickerWidget;
use Filament\Forms\Components\DatePicker;
use Filament\Forms\Form;
use Filament\Pages\Dashboard as BaseDashboard;
use Filament\Pages\Dashboard\Actions\FilterAction;
use Filament\Pages\Dashboard\Concerns\HasFiltersAction;

class Dashboard extends BaseDashboard
{
    use HasFiltersAction;
    protected static ?int $navigationSort = -10;

 
    
    
    protected function getHeaderActions(): array
    {
        return [
     
        ];
    }

    protected function getHeaderWidgets(): array
    {
        return [
          
        ];
    }
}